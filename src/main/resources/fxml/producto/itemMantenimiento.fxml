<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.ProgressIndicator?>
<?import javafx.scene.control.ScrollPane?>
<?import javafx.scene.control.TextArea?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.FlowPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Region?>
<?import javafx.scene.layout.VBox?>
<?import org.controlsfx.control.textfield.CustomPasswordField?>
<?import org.controlsfx.control.textfield.CustomTextField?>
<?import org.kordamp.ikonli.javafx.FontIcon?>

<AnchorPane stylesheets="@../../css/styles.css" xmlns="http://javafx.com/javafx/23.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="corp.jamaro.jamaroescritoriofx.appfx.producto.controller.ItemMantenimientoController">

   <!-- Header Section -->
   <VBox spacing="9.0" styleClass="header-section" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
      <padding>
         <Insets bottom="9.0" left="9.0" right="9.0" top="9.0" />
      </padding>

      <!-- Title and Action Buttons -->
      <HBox alignment="CENTER_LEFT" spacing="3.0">
         <Label styleClass="page-title" text="Mantenimiento de Item" />
         <Region HBox.hgrow="ALWAYS" />

         <!-- Action Buttons -->
         <HBox alignment="CENTER_RIGHT" spacing="10">
            <Button fx:id="btnGuardar" styleClass="button, primary-button" text="Guardar">
               <graphic>
                  <FontIcon iconLiteral="fas-save" styleClass="font-icon-light" />
               </graphic>
            </Button>
            <Button fx:id="btnCancelar" styleClass="button, secondary-button" text="Cancelar">
               <graphic>
                  <FontIcon iconLiteral="fas-times" styleClass="font-icon-light" />
               </graphic>
            </Button>
         </HBox>
      </HBox>

      <!-- Search Section -->
      <HBox alignment="CENTER_LEFT" spacing="10">
         <Label styleClass="field-label" text="Buscar Producto o Item:" />
         <ProgressIndicator fx:id="progressIndicator" maxHeight="20" maxWidth="20" visible="false" />
         <CustomTextField fx:id="txtBuscarCodProductoOld" prefWidth="450.0" promptText="Ingrese código del producto o item" styleClass="search-field" HBox.hgrow="ALWAYS" />
         <TextField fx:id="txtMarca" prefWidth="333.0" promptText="Marca" />
         <Button fx:id="btnBuscarProducto1" styleClass="button, primary-button" text="">
            <graphic>
               <FontIcon iconLiteral="fas-search" styleClass="font-icon-light" />
            </graphic>
         </Button>
      </HBox>
   </VBox>

   <!-- Main Content Area -->
   <ScrollPane fitToWidth="true" minWidth="1200.0" prefWidth="1200.0" styleClass="main-scroll-pane" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="81.0">

      <VBox spacing="3.0" styleClass="main-content">
         <padding>
            <Insets bottom="9.0" left="9.0" right="9.0" top="9.0" />
         </padding>

         <HBox alignment="CENTER_LEFT" spacing="9.0">
            <children>
               <Label fx:id="lblDescripcion" styleClass="card-title" text="Descripción">
                  <graphic>
                     <FontIcon iconLiteral="fas-info-circle" styleClass="font-icon-light" />
                  </graphic>
               </Label>
               <CustomTextField fx:id="txtDescripcion" promptText="Descripción detallada del item" HBox.hgrow="ALWAYS" />
               <Label styleClass="card-title" text="Categorias Grupos :">
                  <graphic>
                     <FontIcon iconLiteral="fas-layer-group" styleClass="font-icon-light" />
                  </graphic>
               </Label>
               <ScrollPane fitToWidth="true" prefHeight="36.0" prefWidth="270.0" styleClass="year-scroll-pane">
                  <content>
                     <FlowPane fx:id="flowPaneGrupos" hgap="10.0" prefWrapLength="600.0" vgap="10.0" />
                  </content>
               </ScrollPane>
            </children>
            <padding>
               <Insets left="9.0" right="9.0" top="9.0" />
            </padding>
         </HBox>

         <!-- Grupos Card -->
         <VBox spacing="9.0" styleClass="card">

            <HBox alignment="CENTER_LEFT" spacing="9.0">
               <Label fx:id="lblAtributo" styleClass="card-title" text="Atributos">
                  <graphic>
                     <FontIcon iconLiteral="fas-tags" styleClass="font-icon-light" />
                  </graphic>
               </Label>
   
               <!-- Horizontal display of groups using FlowPane -->
               <ScrollPane fitToWidth="true" prefHeight="54.0" prefWidth="300.0" styleClass="year-scroll-pane" HBox.hgrow="ALWAYS">
                  <FlowPane fx:id="flowPaneAtributos" hgap="10.0" prefWrapLength="600.0" vgap="10.0" />
               </ScrollPane>
            </HBox>
            <padding>
               <Insets bottom="9.0" left="9.0" right="9.0" top="9.0" />
            </padding>
         </VBox>
         <HBox>
            <children>
               <VBox spacing="9.0" styleClass="card" HBox.hgrow="ALWAYS">
                  <padding>
                     <Insets bottom="9.0" left="9.0" right="9.0" top="9.0" />
                  </padding>
                  <children>
                     <HBox alignment="CENTER_LEFT" spacing="9.0">
                        <children>
                           <Label prefWidth="123.0" styleClass="card-title" text="Códigos de Fábrica">
                              <graphic>
                                 <FontIcon iconLiteral="fas-barcode" styleClass="font-icon-light" />
                              </graphic>
                           </Label>
                           <CustomTextField fx:id="txtBuscarCodigoFabrica" promptText="Escriba para buscar un código o crear uno nuevo..." HBox.hgrow="ALWAYS" />
                        </children>
                     </HBox>
                     <ScrollPane fitToWidth="true" styleClass="year-scroll-pane">
                        <content>
                           <FlowPane fx:id="flowPaneCodigosFabrica" hgap="10.0" prefWrapLength="600.0" vgap="10.0" />
                        </content>
                     </ScrollPane>
                  </children>
               </VBox>
      
               <!-- Códigos de Fábrica Card -->
               <VBox spacing="9.0" styleClass="card" HBox.hgrow="ALWAYS">
                  <padding>
                     <Insets bottom="9.0" left="9.0" right="9.0" top="9.0" />
                  </padding>
      
                  <HBox alignment="CENTER_LEFT" spacing="10.0">
                     <Label styleClass="card-title" text="Ubicaciones">
                        <graphic>
                           <FontIcon iconLiteral="fas-warehouse" styleClass="font-icon-light" />
                        </graphic>
                     </Label>
                     <CustomTextField fx:id="txtBuscarUbicaciones" promptText="Escriba para buscar un código o crear uno nuevo..." HBox.hgrow="ALWAYS" />
                  </HBox>
      
                  <!-- Horizontal display of factory codes using FlowPane -->
                  <ScrollPane fitToWidth="true" styleClass="year-scroll-pane">
                     <FlowPane fx:id="flowPaneUbicaciones" hgap="10.0" prefWrapLength="600.0" vgap="10.0" />
                  </ScrollPane>
               </VBox>
            </children>
         </HBox>

         <!-- Atributos Card -->
         <VBox spacing="9.0" styleClass="card">
            <padding>
               <Insets bottom="9.0" left="9.0" right="9.0" top="9.0" />
            </padding>

            <HBox alignment="CENTER_LEFT" spacing="9.0">
               <Label styleClass="card-title" text="Precios y Stock">
                  <graphic>
                     <FontIcon iconLiteral="fa-dollar" styleClass="font-icon-light" />
                  </graphic>
               </Label>
               <Label styleClass="label-info" text="rellenar los campos necesarios" />
            </HBox>
            <HBox alignment="CENTER_LEFT" spacing="9.0">
               <children>
                  <Label text="Precio Costo" />
                  <CustomPasswordField fx:id="txtPrecioCostoPromedio" prefWidth="72.0" HBox.hgrow="ALWAYS" />
                  <Label text="P.Venta Base" />
                  <CustomPasswordField fx:id="txtPrecioVentaBase" prefWidth="72.0" HBox.hgrow="ALWAYS" />
                  <Label text="P.Venta Promocion" />
                  <CustomPasswordField fx:id="txtPrecioVentaPromocion" prefWidth="72.0" HBox.hgrow="ALWAYS" />
                  <Label text="P.Venta Publico" />
                  <CustomPasswordField fx:id="txtPrecioVentaPublico" prefWidth="72.0" HBox.hgrow="ALWAYS" />
                  <Label text="Stock" />
                  <CustomPasswordField fx:id="txtStockTotal" prefWidth="72.0" HBox.hgrow="ALWAYS" />
                  <Label text="Stock De Seguridad" />
                  <CustomPasswordField fx:id="txtStockDeSeguridad" prefWidth="72.0" HBox.hgrow="ALWAYS" />
               </children>
            </HBox>
            <TextArea fx:id="txtAnotaciones" prefHeight="90.0" promptText="Anotaciones del Item" />
         </VBox>

         <!-- Archivos/Imágenes Card -->
         <VBox spacing="9.0" styleClass="card">
            <padding>
               <Insets bottom="9.0" left="9.0" right="9.0" top="9.0" />
            </padding>

            <HBox alignment="CENTER_LEFT" spacing="9.0">
               <Label styleClass="card-title" text="Imágenes">
                  <graphic>
                     <FontIcon iconLiteral="fas-images" styleClass="font-icon-light" />
                  </graphic>
               </Label>
               <Region HBox.hgrow="ALWAYS" />
               <Button fx:id="btnAgregarImagen" prefWidth="135.0" styleClass="button, success-button" text="Agregar Imagen">
                  <graphic>
                     <FontIcon iconLiteral="fas-plus" styleClass="font-icon-light" />
                  </graphic>
               </Button>
            </HBox>

            <!-- File Display Area -->
            <ScrollPane fitToHeight="true" prefHeight="90.0" styleClass="file-scroll-pane">
               <FlowPane fx:id="flowPaneArchivos" hgap="10.0" prefWrapLength="600.0" vgap="10.0" />
            </ScrollPane>
         </VBox>

      </VBox>
   </ScrollPane>

</AnchorPane>

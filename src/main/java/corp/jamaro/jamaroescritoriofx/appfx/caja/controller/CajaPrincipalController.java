package corp.jamaro.jamaroescritoriofx.appfx.caja.controller;

import corp.jamaro.jamaroescritoriofx.appfx.caja.model.CobroDineroProgramado;
import corp.jamaro.jamaroescritoriofx.appfx.caja.model.gui.<PERSON>aja<PERSON>ui;
import corp.jamaro.jamaroescritoriofx.appfx.caja.service.CajaCobrosService;
import corp.jamaro.jamaroescritoriofx.appfx.caja.service.CajaGuiService;
import corp.jamaro.jamaroescritoriofx.appfx.caja.util.CajaDialogUtil;
import corp.jamaro.jamaroescritoriofx.appfx.controller.BaseController;
import corp.jamaro.jamaroescritoriofx.appfx.model.FXMLEnum;
import corp.jamaro.jamaroescritoriofx.appfx.service.SpringFXMLLoader;
import corp.jamaro.jamaroescritoriofx.appfx.util.AlertUtil;
import corp.jamaro.jamaroescritoriofx.appfx.util.LoadingUtil;
import javafx.animation.Timeline;
import javafx.animation.KeyFrame;
import javafx.application.Platform;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.event.EventHandler;
import javafx.fxml.FXML;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.input.KeyEvent;
import javafx.scene.input.MouseEvent;
import javafx.scene.layout.AnchorPane;
import javafx.scene.layout.StackPane;
import javafx.scene.layout.VBox;
import javafx.scene.Parent;
import javafx.util.Duration;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.net.URL;
import java.text.NumberFormat;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;
import java.util.ResourceBundle;

/**
 * Controlador principal para la gestión de caja.
 * Maneja la selección de CajaGui y la visualización de cobros pendientes.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class CajaPrincipalController extends BaseController {

    private final CajaGuiService cajaGuiService;
    private final CajaCobrosService cajaCobrosService;
    private final AlertUtil alertUtil;
    private final SpringFXMLLoader springFXMLLoader;
    private final CajaDialogUtil cajaDialogUtil;

    // FXML Components
    @FXML private StackPane mainStackPane;
    @FXML private ProgressIndicator loadingIndicator;
    @FXML private VBox mainContent;
    @FXML private Label cajaNameLabel;
    @FXML private MenuButton cajaMenuButton;
    @FXML private MenuItem menuChangeCaja;
    @FXML private MenuItem menuInitEfectivo;
    @FXML private MenuItem menuCloseEfectivo;
    @FXML private MenuItem menuInitDigital;
    @FXML private MenuItem menuCloseDigital;
    @FXML private CheckBox leerVentaCheckBox;
    @FXML private Button refreshButton;
    @FXML private SplitPane splitPaneMain;
    @FXML private AnchorPane anchorCobroDetail;
    @FXML private TabPane collectionsTabPane;

    // Contado tab components
    @FXML private TableView<CobroDineroProgramado> contadoTableView;
    @FXML private TableColumn<CobroDineroProgramado, String> contadoIdColumn;
    @FXML private TableColumn<CobroDineroProgramado, Double> contadoMontoColumn;
    @FXML private TableColumn<CobroDineroProgramado, Double> contadoRestanteColumn;
    @FXML private TableColumn<CobroDineroProgramado, String> contadoCreadoElColumn;
    @FXML private TableColumn<CobroDineroProgramado, String> contadoFechaColumn;

    // Credito tab components
    @FXML private TableView<CobroDineroProgramado> creditoTableView;
    @FXML private TableColumn<CobroDineroProgramado, String> creditoIdColumn;
    @FXML private TableColumn<CobroDineroProgramado, Double> creditoMontoColumn;
    @FXML private TableColumn<CobroDineroProgramado, Double> creditoRestanteColumn;
    @FXML private TableColumn<CobroDineroProgramado, String> creditoCreadoElColumn;
    @FXML private TableColumn<CobroDineroProgramado, String> creditoFechaColumn;

    // Data
    private CajaGui selectedCajaGui;
    private CobroDineroProgramado selectedCobro;
    private CobroDetailController currentCobroDetailController;
    private final ObservableList<CobroDineroProgramado> contadoCobros = FXCollections.observableArrayList();
    private final ObservableList<CobroDineroProgramado> creditoCobros = FXCollections.observableArrayList();

    // Activity tracking for "Leer Venta" functionality
    private Timeline inactivityTimer;
    private boolean isDialogActive = false;
    private EventHandler<MouseEvent> mouseActivityHandler;
    private EventHandler<KeyEvent> keyActivityHandler;

    // Formatters
    private final NumberFormat currencyFormat = NumberFormat.getCurrencyInstance();
    private final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm");

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        log.info("Inicializando CajaPrincipalController");

        setupTableColumns();
        setupTableData();
        setupActivityTracking();

        // Start by checking if a CajaGui is already selected
        checkCajaGuiSelection();
    }

    /**
     * Configura el seguimiento de actividad para la funcionalidad "Leer Venta"
     */
    private void setupActivityTracking() {
        // Create activity handlers
        mouseActivityHandler = event -> resetInactivityTimer();
        keyActivityHandler = event -> resetInactivityTimer();

        // Add activity listeners to the main stack pane
        mainStackPane.setOnMouseMoved(mouseActivityHandler);
        mainStackPane.setOnMouseClicked(mouseActivityHandler);
        mainStackPane.setOnKeyPressed(keyActivityHandler);
        mainStackPane.setOnKeyTyped(keyActivityHandler);

        // Initialize the inactivity timer
        setupInactivityTimer();

        log.debug("Activity tracking configurado para 'Leer Venta'");
    }

    /**
     * Configura el timer de inactividad
     */
    private void setupInactivityTimer() {
        inactivityTimer = new Timeline(new KeyFrame(Duration.seconds(3), e -> {
            if (leerVentaCheckBox.isSelected() && !isAnyDialogOpen() && selectedCajaGui != null) {
                // Use Platform.runLater to avoid "showAndWait is not allowed during animation" exception
                Platform.runLater(this::showLeerVentaDialog);
            }
        }));
        inactivityTimer.setCycleCount(1);
    }

    /**
     * Reinicia el timer de inactividad
     */
    private void resetInactivityTimer() {
        if (inactivityTimer != null) {
            inactivityTimer.stop();
            if (leerVentaCheckBox.isSelected() && !isAnyDialogOpen() && selectedCajaGui != null) {
                inactivityTimer.play();
            }
        }
    }

    /**
     * Muestra el diálogo para leer venta por ID
     */
    private void showLeerVentaDialog() {
        if (isAnyDialogOpen()) {
            return; // Prevent multiple dialogs
        }

        log.debug("Mostrando diálogo de Leer Venta por inactividad");
        setDialogActive(true);

        Optional<String> cobroId = cajaDialogUtil.showLeerVentaDialog();
        if (cobroId.isPresent() && !cobroId.get().trim().isEmpty()) {
            searchAndLoadCobroById(cobroId.get().trim());
        }

        setDialogActive(false);
        resetInactivityTimer(); // Restart timer after dialog closes
    }

    /**
     * Busca y carga un cobro por su ID
     */
    private void searchAndLoadCobroById(String cobroIdText) {
        try {
            // Try to find the cobro in the current lists
            CobroDineroProgramado foundCobro = findCobroById(cobroIdText);
            if (foundCobro != null) {
                selectAndShowCobro(foundCobro);
                // Automatically open payment processing dialog (like double-click behavior)
                onProcesarCobro(foundCobro);
            } else {
                alertUtil.showWarning("No se encontró un cobro con el ID especificado en los cobros pendientes actuales.");
            }
        } catch (Exception e) {
            log.error("Error al buscar cobro por ID: {}", cobroIdText, e);
            alertUtil.showError("Error al buscar cobro: " + e.getMessage());
        }
    }

    /**
     * Busca un cobro por ID en las listas de contado y credito
     */
    private CobroDineroProgramado findCobroById(String searchText) {
        // Search in contado and credito lists
        for (CobroDineroProgramado cobro : contadoCobros) {
            if (matchesCobroId(cobro, searchText)) {
                return cobro;
            }
        }
        for (CobroDineroProgramado cobro : creditoCobros) {
            if (matchesCobroId(cobro, searchText)) {
                return cobro;
            }
        }
        return null;
    }

    /**
     * Verifica si un cobro coincide con el texto de búsqueda
     */
    private boolean matchesCobroId(CobroDineroProgramado cobro, String searchText) {
        if (cobro.getId() == null) {
            return false;
        }
        String cobroIdStr = cobro.getId().toString();
        // Allow partial matches (first 8 characters or full ID)
        return cobroIdStr.toLowerCase().startsWith(searchText.toLowerCase()) ||
               cobroIdStr.toLowerCase().contains(searchText.toLowerCase());
    }

    /**
     * Selecciona y muestra un cobro específico
     */
    private void selectAndShowCobro(CobroDineroProgramado cobro) {
        // Find which list contains the cobro and select it
        if (contadoCobros.contains(cobro)) {
            collectionsTabPane.getSelectionModel().select(0);
            contadoTableView.getSelectionModel().select(cobro);
            contadoTableView.scrollTo(cobro);
        } else if (creditoCobros.contains(cobro)) {
            collectionsTabPane.getSelectionModel().select(1);
            creditoTableView.getSelectionModel().select(cobro);
            creditoTableView.scrollTo(cobro);
        }

        // Load the cobro details
        onCobroSelected(cobro);
    }

    /**
     * Verifica si hay algún diálogo abierto en el StackPane
     */
    private boolean isAnyDialogOpen() {
        // Check the manual dialog active flag first
        if (isDialogActive) {
            return true;
        }

        // Check if there are any dialogs or popups currently showing
        try {
            // Check for any Dialog windows that are currently showing
            if (javafx.stage.Window.getWindows().stream()
                .anyMatch(window -> window instanceof javafx.stage.Stage && 
                         ((javafx.stage.Stage) window).getModality() != javafx.stage.Modality.NONE &&
                         window.isShowing())) {
                return true;
            }

            // Check for any Alert dialogs
            if (javafx.stage.Window.getWindows().stream()
                .anyMatch(window -> window.getClass().getSimpleName().contains("Alert") && 
                         window.isShowing())) {
                return true;
            }

            // Check for any Dialog instances
            if (javafx.stage.Window.getWindows().stream()
                .anyMatch(window -> window.getClass().getSimpleName().contains("Dialog") && 
                         window.isShowing())) {
                return true;
            }

        } catch (Exception e) {
            log.debug("Error checking for open dialogs: {}", e.getMessage());
            // If there's an error checking, assume no dialogs are open
            return false;
        }

        return false;
    }

    /**
     * Establece el estado de diálogo activo
     */
    private void setDialogActive(boolean active) {
        this.isDialogActive = active;
        if (active) {
            // Stop the timer when dialog is active
            if (inactivityTimer != null) {
                inactivityTimer.stop();
            }
        } else {
            // Restart timer when dialog closes
            resetInactivityTimer();
        }
    }


    /**
     * Configura las columnas de las tablas
     */
    private void setupTableColumns() {
        // Contado table
        setupTableColumnsForType(contadoIdColumn, contadoMontoColumn, contadoRestanteColumn, 
                                contadoCreadoElColumn, contadoFechaColumn);

        // Credito table
        setupTableColumnsForType(creditoIdColumn, creditoMontoColumn, creditoRestanteColumn, 
                                creditoCreadoElColumn, creditoFechaColumn);
    }

    /**
     * Configura las columnas para un tipo específico de tabla
     */
    private void setupTableColumnsForType(
            TableColumn<CobroDineroProgramado, String> idColumn,
            TableColumn<CobroDineroProgramado, Double> montoColumn,
            TableColumn<CobroDineroProgramado, Double> restanteColumn,
            TableColumn<CobroDineroProgramado, String> creadoElColumn,
            TableColumn<CobroDineroProgramado, String> fechaColumn) {

        // Iniciado Por column
        idColumn.setCellValueFactory(cellData -> 
            new javafx.beans.property.SimpleStringProperty(
                cellData.getValue().getIniciadoPor() != null ? 
                    cellData.getValue().getIniciadoPor().getUsername() : "-"
            )
        );

        // Monto column
        montoColumn.setCellValueFactory(new PropertyValueFactory<>("montoACobrar"));
        montoColumn.setCellFactory(column -> new TableCell<CobroDineroProgramado, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(currencyFormat.format(item));
                }
            }
        });

        // Restante column
        restanteColumn.setCellValueFactory(new PropertyValueFactory<>("montoRestante"));
        restanteColumn.setCellFactory(column -> new TableCell<CobroDineroProgramado, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(currencyFormat.format(item));
                }
            }
        });

        // Creado El column
        creadoElColumn.setCellValueFactory(cellData -> 
            new javafx.beans.property.SimpleStringProperty(
                cellData.getValue().getCreadoEl() != null ? 
                    cellData.getValue().getCreadoEl().atZone(java.time.ZoneId.systemDefault()).format(dateFormatter) : 
                    "-"
            )
        );

        // Fecha column
        fechaColumn.setCellValueFactory(cellData -> 
            new javafx.beans.property.SimpleStringProperty(
                cellData.getValue().getFechaLimite() != null ? 
                    cellData.getValue().getFechaLimite().atZone(java.time.ZoneId.systemDefault()).format(dateFormatter) : 
                    "Sin límite"
            )
        );
    }

    /**
     * Configura los datos de las tablas
     */
    private void setupTableData() {
        contadoTableView.setItems(contadoCobros);
        creditoTableView.setItems(creditoCobros);

        // Add row click listeners to all tables
        setupTableRowClickListener(contadoTableView);
        setupTableRowClickListener(creditoTableView);
    }

    /**
     * Configura el listener de click en las filas de una tabla.
     */
    private void setupTableRowClickListener(TableView<CobroDineroProgramado> tableView) {
        tableView.setRowFactory(tv -> {
            TableRow<CobroDineroProgramado> row = new TableRow<>();
            row.setOnMouseClicked(event -> {
                if (!row.isEmpty()) {
                    CobroDineroProgramado cobro = row.getItem();
                    if (event.getClickCount() == 1) {
                        // Single click: select for detail view
                        onCobroSelected(cobro);
                    } else if (event.getClickCount() == 2) {
                        // Double click: process payment
                        onProcesarCobro(cobro);
                    }
                }
            });
            return row;
        });
    }

    /**
     * Maneja la selección de un cobro y carga su detalle.
     */
    private void onCobroSelected(CobroDineroProgramado cobro) {
        if (cobro == null) {
            return;
        }

        log.debug("Cobro seleccionado: {}", cobro.getId());
        this.selectedCobro = cobro;
        loadCobroDetailView();
    }

    /**
     * Carga la vista de detalle del cobro seleccionado.
     */
    private void loadCobroDetailView() {
        if (selectedCobro == null) {
            clearCobroDetailView();
            return;
        }

        try {
            // Load the cobro detail view
            Parent cobroDetailView = springFXMLLoader.load(FXMLEnum.COBRO_DETAIL);
            currentCobroDetailController = springFXMLLoader.getController(cobroDetailView);

            // Set the selected cobro
            currentCobroDetailController.setCobroDineroProgramado(selectedCobro);

            // Update the UI
            Platform.runLater(() -> {
                anchorCobroDetail.getChildren().clear();
                anchorCobroDetail.getChildren().add(cobroDetailView);
                AnchorPane.setTopAnchor(cobroDetailView, 0.0);
                AnchorPane.setBottomAnchor(cobroDetailView, 0.0);
                AnchorPane.setLeftAnchor(cobroDetailView, 0.0);
                AnchorPane.setRightAnchor(cobroDetailView, 0.0);
            });

            log.debug("Vista de detalle de cobro cargada exitosamente");
        } catch (Exception e) {
            log.error("Error al cargar la vista de detalle del cobro", e);
            alertUtil.showError("Error al cargar los detalles del cobro: " + e.getMessage());
        }
    }

    /**
     * Limpia la vista de detalle del cobro.
     */
    private void clearCobroDetailView() {
        Platform.runLater(() -> {
            anchorCobroDetail.getChildren().clear();
            currentCobroDetailController = null;
            selectedCobro = null;
        });
    }

    /**
     * Verifica si hay una CajaGui seleccionada, si no muestra el diálogo de selección
     */
    private void checkCajaGuiSelection() {
        if (selectedCajaGui == null) {
            showCajaGuiSelectionDialog();
        } else {
            showMainInterface();
        }
    }

    /**
     * Muestra el diálogo de selección de CajaGui
     */
    private void showCajaGuiSelectionDialog() {
        log.debug("Mostrando diálogo de selección de CajaGui");

        LoadingUtil.subscribeWithLoading(
            loadingIndicator,
            cajaGuiService.getAllCajaGuiOrderedByCreatedAt().collectList(),
            this::handleCajaGuiListReceived,
            error -> {
                log.error("Error al obtener lista de CajaGui", error);
                alertUtil.showError("Error al cargar las cajas disponibles: " + error.getMessage());
            }
        );
    }

    /**
     * Maneja la lista de CajaGui recibida y muestra el diálogo de selección
     */
    private void handleCajaGuiListReceived(List<CajaGui> cajaGuiList) {
        Platform.runLater(() -> {
            if (cajaGuiList.isEmpty()) {
                showCreateCajaDialog();
                return;
            }

            Optional<CajaDialogUtil.CajaSelectionResult> result = showCajaSelectionDialog(cajaGuiList);
            if (result.isPresent()) {
                CajaDialogUtil.CajaSelectionResult selectionResult = result.get();
                if (selectionResult.isWantsToCreate()) {
                    // Usuario quiere crear una nueva caja
                    showCreateCajaDialog();
                } else if (selectionResult.getSelectedCaja() != null) {
                    // Usuario seleccionó una caja existente
                    selectedCajaGui = selectionResult.getSelectedCaja();
                    showMainInterface();
                }
            } else {
                // Usuario canceló la selección - debe volver a mostrar el diálogo
                log.info("Usuario canceló la selección de caja - mostrando diálogo nuevamente");
                showCajaGuiSelectionDialog();
            }
        });
    }

    /**
     * Muestra el diálogo de selección de caja usando CajaDialogUtil
     */
    private Optional<CajaDialogUtil.CajaSelectionResult> showCajaSelectionDialog(List<CajaGui> cajaGuiList) {
        setDialogActive(true);
        Optional<CajaDialogUtil.CajaSelectionResult> result = cajaDialogUtil.showCajaSelectionDialog(cajaGuiList);
        setDialogActive(false);
        return result;
    }

    /**
     * Muestra el diálogo para crear una nueva caja cuando no hay cajas disponibles
     */
    private void showCreateCajaDialog() {
        setDialogActive(true);
        Optional<CajaDialogUtil.CreateCajaData> createData = cajaDialogUtil.showCreateCajaDialog();
        setDialogActive(false);
        if (createData.isPresent()) {
            CajaDialogUtil.CreateCajaData data = createData.get();
            log.debug("Creando nueva caja con nombre: {}", data.getNombreCaja());

            LoadingUtil.subscribeWithLoading(
                loadingIndicator,
                cajaGuiService.createCajaGui(data.getNombreCaja(), data.getGuiConfig()),
                this::handleCajaCreated,
                error -> {
                    log.error("Error al crear nueva caja", error);
                    alertUtil.showError("Error al crear la caja: " + error.getMessage());
                    // Retry showing the dialog or go back to selection
                    showCajaGuiSelectionDialog();
                }
            );
        } else {
            log.info("Usuario canceló la creación de caja - volviendo al diálogo de selección");
            // User cancelled creation, return to selection dialog
            showCajaGuiSelectionDialog();
        }
    }

    /**
     * Maneja el resultado de la creación de una nueva caja
     */
    private void handleCajaCreated(CajaGui newCaja) {
        Platform.runLater(() -> {
            log.info("Caja creada exitosamente: {}", newCaja.getNombreCaja());
            alertUtil.showInfo("Caja Creada", 
                "La caja '" + newCaja.getNombreCaja() + "' ha sido creada exitosamente.");

            // Set the newly created caja as selected
            selectedCajaGui = newCaja;
            showMainInterface();
        });
    }

    /**
     * Muestra la interfaz principal después de seleccionar una caja
     */
    private void showMainInterface() {
        Platform.runLater(() -> {
            cajaNameLabel.setText("Caja: " + selectedCajaGui.getNombreCaja());
            mainContent.setVisible(true);
            updateMenuItemsState();
            loadPendingCollections();
        });
    }

    /**
     * Carga los cobros pendientes para contado y credito usando suscripciones en tiempo real
     */
    private void loadPendingCollections() {
        log.debug("Cargando cobros pendientes para caja: {}", selectedCajaGui.getNombreCaja());

        // Subscribe to CajaGui changes first
        registerSubscription(
            cajaGuiService.subscribeToCajaGuiChanges(selectedCajaGui.getId())
                .subscribe(
                    cajaGui -> Platform.runLater(() -> {
                        log.debug("Recibiendo actualización de CajaGui: {}", cajaGui.getNombreCaja());
                        selectedCajaGui = cajaGui;
                        cajaNameLabel.setText("Caja: " + selectedCajaGui.getNombreCaja());
                        updateMenuItemsState();
                    }),
                    logError("suscribiendo a cambios de CajaGui")
                )
        );

        // Subscribe to contado collections using real-time subscription
        registerSubscription(
            cajaGuiService.subscribeToCobroDineroProgramadoContadoChanges()
                .subscribe(
                    cobro -> Platform.runLater(() -> {
                        log.debug("Recibiendo actualización de cobro contado: {}", cobro.getId());
                        updateCobroInList(contadoCobros, cobro);
                        // Auto-select first item if no item is currently selected
                        selectFirstAvailableItem();
                    }),
                    logError("suscribiendo a cobros contado")
                )
        );

        // Subscribe to credito collections using real-time subscription
        registerSubscription(
            cajaGuiService.subscribeToCobroDineroProgramadoCreditoChanges()
                .subscribe(
                    cobro -> Platform.runLater(() -> {
                        log.debug("Recibiendo actualización de cobro credito: {}", cobro.getId());
                        updateCobroInList(creditoCobros, cobro);
                        // Auto-select first item if no item is currently selected
                        selectFirstAvailableItem();
                    }),
                    logError("suscribiendo a cobros credito")
                )
        );
    }

    /**
     * Actualiza un cobro en la lista observable correspondiente
     */
    private void updateCobroInList(ObservableList<CobroDineroProgramado> cobrosList, CobroDineroProgramado cobro) {
        // Find existing cobro by ID
        for (int i = 0; i < cobrosList.size(); i++) {
            if (cobrosList.get(i).getId().equals(cobro.getId())) {
                // Update existing cobro
                cobrosList.set(i, cobro);
                return;
            }
        }
        // If not found, add new cobro
        cobrosList.add(cobro);
    }

    /**
     * Maneja el evento de cambio de caja
     */
    @FXML
    private void onChangeCaja() {
        selectedCajaGui = null;
        mainContent.setVisible(false);
        contadoCobros.clear();
        creditoCobros.clear();
        showCajaGuiSelectionDialog();
    }

    /**
     * Selecciona automáticamente el primer item disponible si no hay ninguno seleccionado
     */
    private void selectFirstAvailableItem() {
        if (selectedCobro != null) {
            return; // Ya hay un item seleccionado
        }

        CobroDineroProgramado firstItem = null;

        // Buscar el primer item disponible en orden de prioridad: contado, credito
        if (!contadoCobros.isEmpty()) {
            firstItem = contadoCobros.get(0);
            // Seleccionar en la tabla de contado
            contadoTableView.getSelectionModel().select(firstItem);
            // Cambiar a la pestaña de contado
            collectionsTabPane.getSelectionModel().select(0);
        } else if (!creditoCobros.isEmpty()) {
            firstItem = creditoCobros.get(0);
            // Seleccionar en la tabla de credito
            creditoTableView.getSelectionModel().select(firstItem);
            // Cambiar a la pestaña de credito
            collectionsTabPane.getSelectionModel().select(1);
        }

        if (firstItem != null) {
            onCobroSelected(firstItem);
        }
    }

    /**
     * Maneja el evento de actualización
     */
    @FXML
    private void onRefresh() {
        contadoCobros.clear();
        creditoCobros.clear();
        clearCobroDetailView();
        loadPendingCollections();
    }

    /**
     * Procesa un cobro específico
     */
    private void onProcesarCobro(CobroDineroProgramado cobro) {
        log.debug("Procesando cobro: {}", cobro.getId());

        Optional<CajaDialogUtil.PaymentData> paymentData = showPaymentDialog(cobro);
        if (paymentData.isPresent()) {
            procesarCobroConDatos(cobro, paymentData.get());
        }
    }

    /**
     * Muestra el diálogo de procesamiento de pago usando CajaDialogUtil
     */
    private Optional<CajaDialogUtil.PaymentData> showPaymentDialog(CobroDineroProgramado cobro) {
        setDialogActive(true);
        Optional<CajaDialogUtil.PaymentData> result = cajaDialogUtil.showPaymentDialog(cobro);
        setDialogActive(false);
        return result;
    }

    /**
     * Procesa el cobro con los datos de pago proporcionados
     */
    private void procesarCobroConDatos(CobroDineroProgramado cobro, CajaDialogUtil.PaymentData paymentData) {
        log.debug("Procesando cobro {} con datos: efectivo={}, digital={}", 
                 cobro.getId(), paymentData.getMontoEfectivo(), paymentData.getMontoDigital());

        LoadingUtil.subscribeWithLoading(
            loadingIndicator,
            cajaCobrosService.procesarCobro(
                selectedCajaGui.getId(),
                cobro.getId(),
                paymentData.getMontoEfectivo(),
                paymentData.getMontoDigital(),
                paymentData.getDetallesEfectivo(),
                paymentData.getDetallesDigital()
            ),
            this::handleCobroProcessed,
            error -> {
                log.error("Error al procesar cobro", error);
                handleCobroProcessingError(error.getMessage(), cobro, paymentData);
            }
        );
    }

    /**
     * Maneja el resultado del cobro procesado
     */
    private void handleCobroProcessed(CajaCobrosService.ResultadoCobro resultado) {
        Platform.runLater(() -> {
            if (resultado.isExitoso()) {
                String message = "✅ Cobro procesado exitosamente\n\n" + resultado.getMensaje();
                if (resultado.getVuelto() != null && resultado.getVuelto() > 0) {
                    message += "\n\n💰 Vuelto: " + currencyFormat.format(resultado.getVuelto());
                }
                alertUtil.showInfo("Cobro Procesado", message);

                // Refresh the collections to reflect the changes
                onRefresh();
            } else {
                handleCobroResultError(resultado.getMensaje());
            }
        });
    }

    /**
     * Maneja errores durante el procesamiento de cobros desde el servicio
     */
    private void handleCobroProcessingError(String errorMessage, CobroDineroProgramado cobro, CajaDialogUtil.PaymentData paymentData) {
        Platform.runLater(() -> {
            if (isCashRegisterConfigurationError(errorMessage)) {
                showCashRegisterInitializationOptions(errorMessage, cobro, paymentData);
            } else {
                alertUtil.showError("Error al procesar el cobro: " + errorMessage);
            }
        });
    }

    /**
     * Maneja errores del resultado del cobro
     */
    private void handleCobroResultError(String errorMessage) {
        if (isCashRegisterConfigurationError(errorMessage)) {
            showCashRegisterInitializationOptions(errorMessage, null, null);
        } else {
            alertUtil.showError("Error en el cobro: " + errorMessage);
        }
    }

    /**
     * Verifica si el error es relacionado con configuración de caja de dinero
     */
    private boolean isCashRegisterConfigurationError(String errorMessage) {
        if (errorMessage == null) {
            return false;
        }

        String lowerMessage = errorMessage.toLowerCase();
        return lowerMessage.contains("no tiene configurada una caja de dinero en efectivo") ||
               lowerMessage.contains("no tiene configurada una caja digital") ||
               lowerMessage.contains("configure una cajadineroefectivo") ||
               lowerMessage.contains("configure una cajadinerodigital") ||
               lowerMessage.contains("caja de efectivo") && lowerMessage.contains("no") ||
               lowerMessage.contains("caja digital") && lowerMessage.contains("no");
    }

    /**
     * Muestra opciones para inicializar cajas de dinero cuando hay errores de configuración
     */
    private void showCashRegisterInitializationOptions(String errorMessage, CobroDineroProgramado cobro, CajaDialogUtil.PaymentData paymentData) {
        setDialogActive(true); // Prevent "Leer Venta" dialog from appearing

        try {
            String lowerMessage = errorMessage.toLowerCase();
            boolean isEffectivoError = lowerMessage.contains("efectivo") || lowerMessage.contains("cajadineroefectivo");
            boolean isDigitalError = lowerMessage.contains("digital") || lowerMessage.contains("cajadinerodigital");

            String title = "Error de Configuración de Caja";
            String message = "Error: " + errorMessage + "\n\n¿Desea inicializar la caja ahora?";

            Alert alert = new Alert(Alert.AlertType.CONFIRMATION);
            alert.setTitle(title);
            alert.setHeaderText("Caja no configurada");
            alert.setContentText(message);

            // Crear botones personalizados
            ButtonType initEfectivoButton = new ButtonType("Inicializar Caja Efectivo");
            ButtonType initDigitalButton = new ButtonType("Inicializar Caja Digital");
            ButtonType cancelButton = new ButtonType("Cancelar", ButtonBar.ButtonData.CANCEL_CLOSE);

            if (isEffectivoError && !isDigitalError) {
                // Solo error de efectivo
                alert.getButtonTypes().setAll(initEfectivoButton, cancelButton);
            } else if (isDigitalError && !isEffectivoError) {
                // Solo error digital
                alert.getButtonTypes().setAll(initDigitalButton, cancelButton);
            } else {
                // Error general o ambos tipos
                alert.getButtonTypes().setAll(initEfectivoButton, initDigitalButton, cancelButton);
            }

            Optional<ButtonType> result = alert.showAndWait();
            if (result.isPresent()) {
                if (result.get() == initEfectivoButton) {
                    initializeCajaEfectivoAndRetryPayment(cobro, paymentData);
                } else if (result.get() == initDigitalButton) {
                    initializeCajaDigitalAndRetryPayment(cobro, paymentData);
                }
                // Si es cancelar, no hacer nada
            }
        } finally {
            setDialogActive(false); // Re-enable "Leer Venta" dialog
        }
    }

    /**
     * Inicializa caja de efectivo y reintenta el pago si es posible
     */
    private void initializeCajaEfectivoAndRetryPayment(CobroDineroProgramado cobro, CajaDialogUtil.PaymentData paymentData) {
        if (selectedCajaGui.getCajaDineroEfectivo() != null) {
            alertUtil.showWarning("Esta caja ya tiene una caja de efectivo iniciada.");
            return;
        }

        setDialogActive(true); // Prevent "Leer Venta" dialog from appearing
        Optional<CajaDialogUtil.InitializeCajaEfectivoData> result = cajaDialogUtil.showInitializeCajaEfectivoDialog();
        setDialogActive(false); // Re-enable "Leer Venta" dialog

        if (result.isPresent()) {
            CajaDialogUtil.InitializeCajaEfectivoData data = result.get();

            LoadingUtil.subscribeWithLoading(
                loadingIndicator,
                cajaGuiService.initializeCajaDineroEfectivo(
                    selectedCajaGui.getId(),
                    data.getNombre(),
                    data.getMontoInicial(),
                    data.getInicioDiezCentimos(),
                    data.getInicioVeinteCentimos(),
                    data.getInicioCincuentaCentimos(),
                    data.getInicioUnSol(),
                    data.getInicioDosSoles(),
                    data.getInicioCincoSoles(),
                    data.getInicioDiezSoles(),
                    data.getInicioVeinteSoles(),
                    data.getInicioCincuentaSoles(),
                    data.getInicioCienSoles(),
                    data.getInicioDoscientosSoles()
                ),
                updatedCaja -> {
                    selectedCajaGui = updatedCaja;
                    alertUtil.showInfo("Caja de efectivo iniciada", "La caja de efectivo ha sido iniciada exitosamente.");
                    updateMenuItemsState();

                    // Reintentar el pago si hay datos disponibles
                    if (cobro != null && paymentData != null) {
                        Platform.runLater(() -> {
                            alertUtil.showInfo("Reintentando pago", "Ahora se reintentará el procesamiento del pago.");
                            procesarCobroConDatos(cobro, paymentData);
                        });
                    }
                },
                error -> {
                    log.error("Error al inicializar caja de efectivo", error);
                    alertUtil.showError("Error al inicializar la caja de efectivo: " + error.getMessage());
                }
            );
        }
    }

    /**
     * Inicializa caja digital y reintenta el pago si es posible
     */
    private void initializeCajaDigitalAndRetryPayment(CobroDineroProgramado cobro, CajaDialogUtil.PaymentData paymentData) {
        if (selectedCajaGui.getCajaDineroDigital() != null) {
            alertUtil.showWarning("Esta caja ya tiene una caja digital iniciada.");
            return;
        }

        setDialogActive(true); // Prevent "Leer Venta" dialog from appearing
        Optional<CajaDialogUtil.InitializeCajaDigitalData> result = cajaDialogUtil.showInitializeCajaDigitalDialog();
        setDialogActive(false); // Re-enable "Leer Venta" dialog

        if (result.isPresent()) {
            CajaDialogUtil.InitializeCajaDigitalData data = result.get();

            LoadingUtil.subscribeWithLoading(
                loadingIndicator,
                cajaGuiService.initializeCajaDineroDigital(
                    selectedCajaGui.getId(),
                    data.getCuentaDigitalAsignada(),
                    data.getMontoInicialDigital()
                ),
                updatedCaja -> {
                    selectedCajaGui = updatedCaja;
                    alertUtil.showInfo("Caja digital iniciada", "La caja digital ha sido iniciada exitosamente.");
                    updateMenuItemsState();

                    // Reintentar el pago si hay datos disponibles
                    if (cobro != null && paymentData != null) {
                        Platform.runLater(() -> {
                            alertUtil.showInfo("Reintentando pago", "Ahora se reintentará el procesamiento del pago.");
                            procesarCobroConDatos(cobro, paymentData);
                        });
                    }
                },
                error -> {
                    log.error("Error al inicializar caja digital", error);
                    alertUtil.showError("Error al inicializar la caja digital: " + error.getMessage());
                }
            );
        }
    }

    /**
     * Maneja el evento de inicializar caja de efectivo
     */
    @FXML
    private void onInitializeCajaEfectivo() {
        if (selectedCajaGui == null) {
            alertUtil.showWarning("No hay caja seleccionada. Debe seleccionar una caja antes de inicializar la caja de efectivo.");
            return;
        }

        if (selectedCajaGui.getCajaDineroEfectivo() != null) {
            alertUtil.showWarning("Esta caja ya tiene una caja de efectivo iniciada.");
            return;
        }

        showInitializeCajaEfectivoDialog();
    }

    /**
     * Maneja el evento de cerrar caja de efectivo
     */
    @FXML
    private void onCloseCajaEfectivo() {
        if (selectedCajaGui == null) {
            alertUtil.showWarning("No hay caja seleccionada. Debe seleccionar una caja antes de cerrar la caja de efectivo.");
            return;
        }

        if (selectedCajaGui.getCajaDineroEfectivo() == null) {
            alertUtil.showWarning("Esta caja no tiene una caja de efectivo iniciada para cerrar.");
            return;
        }

        showCloseCajaEfectivoDialog();
    }

    /**
     * Maneja el evento de inicializar caja digital
     */
    @FXML
    private void onInitializeCajaDigital() {
        if (selectedCajaGui == null) {
            alertUtil.showWarning("No hay caja seleccionada. Debe seleccionar una caja antes de inicializar la caja digital.");
            return;
        }

        if (selectedCajaGui.getCajaDineroDigital() != null) {
            alertUtil.showWarning("Esta caja ya tiene una caja digital iniciada.");
            return;
        }

        showInitializeCajaDigitalDialog();
    }

    /**
     * Maneja el evento de cerrar caja digital
     */
    @FXML
    private void onCloseCajaDigital() {
        if (selectedCajaGui == null) {
            alertUtil.showWarning("No hay caja seleccionada. Debe seleccionar una caja antes de cerrar la caja digital.");
            return;
        }

        if (selectedCajaGui.getCajaDineroDigital() == null) {
            alertUtil.showWarning("Esta caja no tiene una caja digital iniciada para cerrar.");
            return;
        }

        showCloseCajaDigitalDialog();
    }

    /**
     * Muestra el diálogo para inicializar caja de efectivo
     */
    private void showInitializeCajaEfectivoDialog() {
        Optional<CajaDialogUtil.InitializeCajaEfectivoData> result = cajaDialogUtil.showInitializeCajaEfectivoDialog();
        if (result.isPresent()) {
            CajaDialogUtil.InitializeCajaEfectivoData data = result.get();

            LoadingUtil.subscribeWithLoading(
                loadingIndicator,
                cajaGuiService.initializeCajaDineroEfectivo(
                    selectedCajaGui.getId(),
                    data.getNombre(),
                    data.getMontoInicial(),
                    data.getInicioDiezCentimos(),
                    data.getInicioVeinteCentimos(),
                    data.getInicioCincuentaCentimos(),
                    data.getInicioUnSol(),
                    data.getInicioDosSoles(),
                    data.getInicioCincoSoles(),
                    data.getInicioDiezSoles(),
                    data.getInicioVeinteSoles(),
                    data.getInicioCincuentaSoles(),
                    data.getInicioCienSoles(),
                    data.getInicioDoscientosSoles()
                ),
                this::handleCajaEfectivoInitialized,
                error -> {
                    log.error("Error al inicializar caja de efectivo", error);
                    alertUtil.showError("Error al inicializar la caja de efectivo: " + error.getMessage());
                }
            );
        }
    }

    /**
     * Muestra el diálogo para cerrar caja de efectivo
     */
    private void showCloseCajaEfectivoDialog() {
        Optional<CajaDialogUtil.CloseCajaEfectivoData> result = cajaDialogUtil.showCloseCajaEfectivoDialog();
        if (result.isPresent()) {
            CajaDialogUtil.CloseCajaEfectivoData data = result.get();

            LoadingUtil.subscribeWithLoading(
                loadingIndicator,
                cajaGuiService.closeCajaDineroEfectivo(
                    selectedCajaGui.getId(),
                    selectedCajaGui.getCajaDineroEfectivo().getId(),
                    data.getCierreDiezCentimos(),
                    data.getCierreVeinteCentimos(),
                    data.getCierreCincuentaCentimos(),
                    data.getCierreUnSol(),
                    data.getCierreDosSoles(),
                    data.getCierreCincoSoles(),
                    data.getCierreDiezSoles(),
                    data.getCierreVeinteSoles(),
                    data.getCierreCincuentaSoles(),
                    data.getCierreCienSoles(),
                    data.getCierreDoscientosSoles()
                ),
                this::handleCajaEfectivoClosed,
                error -> {
                    log.error("Error al cerrar caja de efectivo", error);
                    alertUtil.showError("Error al cerrar la caja de efectivo: " + error.getMessage());
                }
            );
        }
    }

    /**
     * Muestra el diálogo para inicializar caja digital
     */
    private void showInitializeCajaDigitalDialog() {
        Optional<CajaDialogUtil.InitializeCajaDigitalData> result = cajaDialogUtil.showInitializeCajaDigitalDialog();
        if (result.isPresent()) {
            CajaDialogUtil.InitializeCajaDigitalData data = result.get();

            LoadingUtil.subscribeWithLoading(
                loadingIndicator,
                cajaGuiService.initializeCajaDineroDigital(
                    selectedCajaGui.getId(),
                    data.getCuentaDigitalAsignada(),
                    data.getMontoInicialDigital()
                ),
                this::handleCajaDigitalInitialized,
                error -> {
                    log.error("Error al inicializar caja digital", error);
                    alertUtil.showError("Error al inicializar la caja digital: " + error.getMessage());
                }
            );
        }
    }

    /**
     * Muestra el diálogo para cerrar caja digital
     */
    private void showCloseCajaDigitalDialog() {
        Optional<CajaDialogUtil.CloseCajaDigitalData> result = cajaDialogUtil.showCloseCajaDigitalDialog();
        if (result.isPresent()) {
            CajaDialogUtil.CloseCajaDigitalData data = result.get();

            LoadingUtil.subscribeWithLoading(
                loadingIndicator,
                cajaGuiService.closeCajaDineroDigital(
                    selectedCajaGui.getId(),
                    selectedCajaGui.getCajaDineroDigital().getId(),
                    data.getCierreDigitalDeclarado()
                ),
                this::handleCajaDigitalClosed,
                error -> {
                    log.error("Error al cerrar caja digital", error);
                    alertUtil.showError("Error al cerrar la caja digital: " + error.getMessage());
                }
            );
        }
    }

    /**
     * Maneja el resultado de inicializar caja de efectivo
     */
    private void handleCajaEfectivoInitialized(CajaGui updatedCaja) {
        Platform.runLater(() -> {
            selectedCajaGui = updatedCaja;
            alertUtil.showInfo("Caja de efectivo iniciada", "La caja de efectivo ha sido iniciada exitosamente.");
            updateMenuItemsState();
        });
    }

    /**
     * Maneja el resultado de cerrar caja de efectivo
     */
    private void handleCajaEfectivoClosed(CajaGui updatedCaja) {
        Platform.runLater(() -> {
            selectedCajaGui = updatedCaja;
            alertUtil.showInfo("Caja de efectivo cerrada", "La caja de efectivo ha sido cerrada exitosamente.");
            updateMenuItemsState();
        });
    }

    /**
     * Maneja el resultado de inicializar caja digital
     */
    private void handleCajaDigitalInitialized(CajaGui updatedCaja) {
        Platform.runLater(() -> {
            selectedCajaGui = updatedCaja;
            alertUtil.showInfo("Caja digital iniciada", "La caja digital ha sido iniciada exitosamente.");
            updateMenuItemsState();
        });
    }

    /**
     * Maneja el resultado de cerrar caja digital
     */
    private void handleCajaDigitalClosed(CajaGui updatedCaja) {
        Platform.runLater(() -> {
            selectedCajaGui = updatedCaja;
            alertUtil.showInfo("Caja digital cerrada", "La caja digital ha sido cerrada exitosamente.");
            updateMenuItemsState();
        });
    }

    /**
     * Actualiza el estado de los elementos del menú según el estado de la caja
     */
    private void updateMenuItemsState() {
        if (selectedCajaGui == null) {
            menuInitEfectivo.setDisable(true);
            menuCloseEfectivo.setDisable(true);
            menuInitDigital.setDisable(true);
            menuCloseDigital.setDisable(true);
            return;
        }

        // Estado de caja de efectivo
        boolean hasEfectivo = selectedCajaGui.getCajaDineroEfectivo() != null;
        menuInitEfectivo.setDisable(hasEfectivo);
        menuCloseEfectivo.setDisable(!hasEfectivo);

        // Estado de caja digital
        boolean hasDigital = selectedCajaGui.getCajaDineroDigital() != null;
        menuInitDigital.setDisable(hasDigital);
        menuCloseDigital.setDisable(!hasDigital);
    }

}

package corp.jamaro.jamaroescritoriofx.appfx.caja.controller;

import corp.jamaro.jamaroescritoriofx.appfx.caja.model.CobroDineroProgramado;
import corp.jamaro.jamaroescritoriofx.appfx.caja.service.CajaCobrosService;
import corp.jamaro.jamaroescritoriofx.appfx.caja.util.CajaDialogUtil;
import corp.jamaro.jamaroescritoriofx.appfx.controller.BaseController;
import corp.jamaro.jamaroescritoriofx.appfx.util.AlertUtil;
import corp.jamaro.jamaroescritoriofx.appfx.util.LoadingUtil;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.model.BienServicioCargado;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.model.Sale;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.service.SaleService;
import javafx.application.Platform;
import javafx.fxml.FXML;
import javafx.geometry.Insets;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.layout.AnchorPane;
import javafx.scene.layout.HBox;
import javafx.scene.layout.StackPane;
import javafx.scene.layout.VBox;
import javafx.scene.text.Font;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.net.URL;
import java.text.NumberFormat;
import java.time.format.DateTimeFormatter;
import java.util.Optional;
import java.util.ResourceBundle;

/**
 * Controlador para mostrar los detalles de un CobroDineroProgramado
 * y la información de la venta asociada.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class CobroDetailController extends BaseController {

    private final SaleService saleService;
    private final CajaCobrosService cajaCobrosService;
    private final AlertUtil alertUtil;
    private final CajaDialogUtil cajaDialogUtil;

    // FXML Components
    @FXML private StackPane stackPaneCobroDetail;
    @FXML private AnchorPane anchorCobroDetail;
    @FXML private ProgressIndicator loadingIndicator;

    // Customer/Cobro info
    @FXML private Label lblClienteInfo;

    // Cobro totals section
    @FXML private VBox vbTotalCobro;
    @FXML private Label lblMontoACobrar;
    @FXML private Label lblMontoRestante;
    @FXML private Label lblIniciadoPor;
    @FXML private Label lblFechaLimite;

    // Sale info display
    @FXML private ScrollPane scrollPaneSaleInfo;
    @FXML private VBox vbSaleInfo;

    // Action buttons
    @FXML private AnchorPane anchorBotones;
    @FXML private Button btnRefresh;
    @FXML private Button btnVerVenta;
    @FXML private Button btnCobrar;

    // Data
    private CobroDineroProgramado currentCobro;
    private Sale currentSale;

    // Formatters
    private final NumberFormat currencyFormat = NumberFormat.getCurrencyInstance();
    private final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm");

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        log.info("Inicializando CobroDetailController");
        clearDisplay();
    }

    /**
     * Establece el CobroDineroProgramado a mostrar y carga la información de la venta asociada.
     */
    public void setCobroDineroProgramado(CobroDineroProgramado cobro) {
        if (cobro == null) {
            clearDisplay();
            return;
        }

        this.currentCobro = cobro;
        updateCobroDisplay();
        loadSaleInformation();
    }

    /**
     * Actualiza la visualización de la información del cobro.
     */
    private void updateCobroDisplay() {
        Platform.runLater(() -> {
            lblIniciadoPor.setText("Iniciado por: " + (currentCobro.getIniciadoPor() != null ? currentCobro.getIniciadoPor() : "-"));
            lblMontoACobrar.setText(currentCobro.getMontoACobrar() != null ? 
                currencyFormat.format(currentCobro.getMontoACobrar()) : "0.00");
            lblMontoRestante.setText(currentCobro.getMontoRestante() != null ? 
                currencyFormat.format(currentCobro.getMontoRestante()) : "0.00");
            lblFechaLimite.setText("Fecha límite: " + (currentCobro.getFechaLimite() != null ? 
                currentCobro.getFechaLimite().atZone(java.time.ZoneId.systemDefault()).format(dateFormatter) : 
                "Sin límite"));
        });
    }

    /**
     * Carga la información de la venta asociada al cobro.
     */
    private void loadSaleInformation() {
        log.debug("Cargando información de venta para cobro: {}", currentCobro.getId());

        LoadingUtil.subscribeWithLoading(
            loadingIndicator,
            saleService.getSaleByCobroDineroProgramadoId(currentCobro.getId()),
            this::handleSaleLoaded,
            error -> {
                log.error("Error al cargar información de venta", error);
                Platform.runLater(() -> {
                    clearSaleDisplay();
                    alertUtil.showError("Error al cargar la información de la venta: " + error.getMessage());
                });
            }
        );
    }

    /**
     * Maneja la venta cargada y actualiza la visualización.
     */
    private void handleSaleLoaded(Sale sale) {
        Platform.runLater(() -> {
            this.currentSale = sale;
            updateSaleDisplay();
        });
    }

    /**
     * Actualiza la visualización de la información de la venta.
     */
    private void updateSaleDisplay() {
        Platform.runLater(() -> {
            if (currentSale == null) {
                clearSaleDisplay();
                return;
            }

            // Update customer info
            lblClienteInfo.setText("Cliente: " + getClienteDisplayName(currentSale.getCliente()));

            // Clear previous content
            vbSaleInfo.getChildren().clear();

            // Sale basic info
            addSaleInfoRow("ID de Venta:", 
                currentSale.getId() != null ? currentSale.getId().toString().substring(0, 8) + "..." : "-");
            addSaleInfoRow("Iniciada por:", 
                currentSale.getIniciadaPor() != null ? currentSale.getIniciadaPor().getUsername() : "-");
            addSaleInfoRow("Tipo de Venta:", 
                currentSale.getTipoVenta() != null ? currentSale.getTipoVenta().toString() : "-");

            // Sale amounts
            addSaleInfoRow("Monto inicial:", 
                currentSale.getTotalMontoInicial() != null ? 
                    currencyFormat.format(currentSale.getTotalMontoInicial()) : "-");
            addSaleInfoRow("Monto acordado:", 
                currentSale.getTotalMontoAcordado() != null ? 
                    currencyFormat.format(currentSale.getTotalMontoAcordado()) : "-");
            addSaleInfoRow("Monto restante:", 
                currentSale.getTotalRestante() != null ? 
                    currencyFormat.format(currentSale.getTotalRestante()) : "-");

            // Sale status
            addSaleInfoRow("Estado:", 
                Boolean.TRUE.equals(currentSale.getEstaPagadoEntregado()) ? "Pagado/Entregado" : "Pendiente");

            // Items count
            int itemsCount = currentSale.getBienServicioCargados() != null ? 
                currentSale.getBienServicioCargados().size() : 0;
            addSaleInfoRow("Cantidad de items:", String.valueOf(itemsCount));

            // Creation date
            addSaleInfoRow("Fecha de creación:", 
                currentSale.getCreatedAt() != null ? 
                    currentSale.getCreatedAt().atZone(java.time.ZoneId.systemDefault()).format(dateFormatter) : "-");

            // Add items table
            addItemsTable();
        });
    }

    /**
     * Obtiene el nombre a mostrar para un cliente.
     */
    private String getClienteDisplayName(corp.jamaro.jamaroescritoriofx.appfx.model.Cliente cliente) {
        if (cliente == null) {
            return "Venta genérica";
        }

        // Si tiene razón social, usarla
        if (cliente.getRazonSocial() != null && !cliente.getRazonSocial().trim().isEmpty()) {
            return cliente.getRazonSocial().trim();
        }

        // Si tiene nombre y apellido, combinarlos
        if (cliente.getNombre() != null && !cliente.getNombre().trim().isEmpty()) {
            String nombre = cliente.getNombre().trim();
            if (cliente.getApellido() != null && !cliente.getApellido().trim().isEmpty()) {
                return nombre + " " + cliente.getApellido().trim();
            }
            return nombre;
        }

        // Si solo tiene apellido
        if (cliente.getApellido() != null && !cliente.getApellido().trim().isEmpty()) {
            return cliente.getApellido().trim();
        }

        // Si tiene algún documento, mostrar "Cliente con [documento]"
        if (cliente.getDni() != null && !cliente.getDni().trim().isEmpty()) {
            return "Cliente con DNI: " + cliente.getDni().trim();
        }
        if (cliente.getRuc() != null && !cliente.getRuc().trim().isEmpty()) {
            return "Cliente con RUC: " + cliente.getRuc().trim();
        }
        if (cliente.getOtroDocumento() != null && !cliente.getOtroDocumento().trim().isEmpty()) {
            return "Cliente con Doc: " + cliente.getOtroDocumento().trim();
        }

        return "Cliente sin nombre";
    }

    /**
     * Agrega una fila de información a la visualización de la venta.
     */
    private void addSaleInfoRow(String label, String value) {
        HBox row = new HBox(10);

        Label labelControl = new Label(label);
        labelControl.setFont(Font.font("System Bold", 12));
        labelControl.setMinWidth(120);

        Label valueControl = new Label(value);
        valueControl.setFont(Font.font("System", 12));

        row.getChildren().addAll(labelControl, valueControl);
        vbSaleInfo.getChildren().add(row);
    }

    /**
     * Agrega una tabla con los items de la venta (bienServicioCargados).
     */
    private void addItemsTable() {
        if (currentSale == null || currentSale.getBienServicioCargados() == null || 
            currentSale.getBienServicioCargados().isEmpty()) {
            return;
        }

        // Add separator and title
        Label titleLabel = new Label("Items de la Venta:");
        titleLabel.setFont(Font.font("System Bold", 14));
        titleLabel.setPadding(new Insets(10, 0, 5, 0));
        vbSaleInfo.getChildren().add(titleLabel);

        // Create table
        TableView<BienServicioCargado> tableView = new TableView<>();
        tableView.setPrefHeight(200);
        tableView.setMaxHeight(300);

        // Create columns
        TableColumn<BienServicioCargado, String> codCompuestoCol = new TableColumn<>("Código");
        codCompuestoCol.setCellValueFactory(cellData -> {
            BienServicioCargado item = cellData.getValue();
            String codCompuesto = (item.getItem() != null && item.getItem().getCodCompuesto() != null) 
                ? item.getItem().getCodCompuesto() : "-";
            return new javafx.beans.property.SimpleStringProperty(codCompuesto);
        });
        codCompuestoCol.setPrefWidth(100);

        TableColumn<BienServicioCargado, String> descripcionCol = new TableColumn<>("Descripción");
        descripcionCol.setCellValueFactory(cellData -> {
            String descripcion = cellData.getValue().getDescripcionDelBienServicio();
            return new javafx.beans.property.SimpleStringProperty(descripcion != null ? descripcion : "-");
        });
        descripcionCol.setPrefWidth(200);

        TableColumn<BienServicioCargado, String> precioInicialCol = new TableColumn<>("Precio Inicial");
        precioInicialCol.setCellValueFactory(cellData -> {
            Double precio = cellData.getValue().getPrecioInicial();
            String formatted = precio != null ? currencyFormat.format(precio) : "-";
            return new javafx.beans.property.SimpleStringProperty(formatted);
        });
        precioInicialCol.setPrefWidth(100);

        TableColumn<BienServicioCargado, String> precioAcordadoCol = new TableColumn<>("Precio Acordado");
        precioAcordadoCol.setCellValueFactory(cellData -> {
            Double precio = cellData.getValue().getPrecioAcordado();
            String formatted = precio != null ? currencyFormat.format(precio) : "-";
            return new javafx.beans.property.SimpleStringProperty(formatted);
        });
        precioAcordadoCol.setPrefWidth(110);

        TableColumn<BienServicioCargado, String> cantidadCol = new TableColumn<>("Cantidad");
        cantidadCol.setCellValueFactory(cellData -> {
            Double cantidad = cellData.getValue().getCantidad();
            String formatted = cantidad != null ? String.format("%.2f", cantidad) : "-";
            return new javafx.beans.property.SimpleStringProperty(formatted);
        });
        cantidadCol.setPrefWidth(80);

        TableColumn<BienServicioCargado, String> montoAcordadoCol = new TableColumn<>("Monto Acordado");
        montoAcordadoCol.setCellValueFactory(cellData -> {
            Double monto = cellData.getValue().getMontoAcordado();
            String formatted = monto != null ? currencyFormat.format(monto) : "-";
            return new javafx.beans.property.SimpleStringProperty(formatted);
        });
        montoAcordadoCol.setPrefWidth(120);

        // Add columns to table
        tableView.getColumns().add(codCompuestoCol);
        tableView.getColumns().add(descripcionCol);
        tableView.getColumns().add(precioInicialCol);
        tableView.getColumns().add(precioAcordadoCol);
        tableView.getColumns().add(cantidadCol);
        tableView.getColumns().add(montoAcordadoCol);

        // Set data
        tableView.getItems().addAll(currentSale.getBienServicioCargados());

        // Add table to container
        vbSaleInfo.getChildren().add(tableView);
    }

    /**
     * Limpia la visualización completa.
     */
    private void clearDisplay() {
        Platform.runLater(() -> {
            clearCobroDisplay();
            clearSaleDisplay();
        });
    }

    /**
     * Limpia la visualización del cobro.
     */
    private void clearCobroDisplay() {
        lblIniciadoPor.setText("Iniciado por: -");
        lblMontoACobrar.setText("0.00");
        lblMontoRestante.setText("0.00");
        lblFechaLimite.setText("Fecha límite: -");
        lblClienteInfo.setText("Cliente: -");
    }

    /**
     * Limpia la visualización de la venta.
     */
    private void clearSaleDisplay() {
        vbSaleInfo.getChildren().clear();
    }

    // Button handlers

    /**
     * Maneja el evento del botón Actualizar.
     */
    @FXML
    private void handleBtnRefresh() {
        if (currentCobro != null) {
            log.debug("Actualizando información del cobro: {}", currentCobro.getId());
            loadSaleInformation();
        }
    }

    /**
     * Maneja el evento del botón Ver Venta - ahora llama al diálogo de Leer Venta.
     */
    @FXML
    private void handleBtnVerVenta() {
        log.debug("Mostrando diálogo de Leer Venta desde botón Ver Venta");

        Optional<String> cobroId = cajaDialogUtil.showLeerVentaDialog();
        if (cobroId.isPresent() && !cobroId.get().trim().isEmpty()) {
            // Show info that the search functionality is handled by the main controller
            alertUtil.showInfo("Búsqueda de Cobro", 
                "La búsqueda de cobros por ID se maneja desde la vista principal de caja.\n" +
                "ID ingresado: " + cobroId.get().trim());
        }
    }

    /**
     * Maneja el evento del botón Cobrar.
     */
    @FXML
    private void handleBtnCobrar() {
        if (currentCobro != null) {
            log.debug("Iniciando proceso de cobro para: {}", currentCobro.getId());
            showPaymentDialog();
        } else {
            alertUtil.showWarning("No hay cobro seleccionado para procesar.");
        }
    }

    /**
     * Muestra el diálogo de procesamiento de pago usando CajaDialogUtil.
     */
    private void showPaymentDialog() {
        Optional<CajaDialogUtil.PaymentData> result = cajaDialogUtil.showPaymentDialog(currentCobro);
        if (result.isPresent()) {
            procesarCobroConDatos(result.get());
        }
    }

    /**
     * Procesa el cobro con los datos de pago proporcionados.
     */
    private void procesarCobroConDatos(CajaDialogUtil.PaymentData paymentData) {
        log.debug("Procesando cobro {} con datos: efectivo={}, digital={}", 
                 currentCobro.getId(), paymentData.getMontoEfectivo(), paymentData.getMontoDigital());

        // Note: We need a reference to CajaGui to process the payment
        // For now, we'll show an info message
        alertUtil.showInfo("Funcionalidad en desarrollo", 
            "El procesamiento de cobros desde esta vista estará disponible próximamente.\n" +
            "Por favor, use la tabla principal para procesar cobros.");
    }

    /**
     * Obtiene el cobro actualmente mostrado.
     */
    public CobroDineroProgramado getCurrentCobro() {
        return currentCobro;
    }

    /**
     * Obtiene la venta actualmente mostrada.
     */
    public Sale getCurrentSale() {
        return currentSale;
    }

}

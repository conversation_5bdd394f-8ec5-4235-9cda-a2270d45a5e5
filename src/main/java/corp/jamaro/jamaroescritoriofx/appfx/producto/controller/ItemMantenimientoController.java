package corp.jamaro.jamaroescritoriofx.appfx.producto.controller;

import corp.jamaro.jamaroescritoriofx.appfx.controller.BaseController;
import corp.jamaro.jamaroescritoriofx.appfx.producto.model.*;
import corp.jamaro.jamaroescritoriofx.appfx.producto.model.enums.TipoFiltro;
import corp.jamaro.jamaroescritoriofx.appfx.producto.service.ItemMantenimientoService;
import corp.jamaro.jamaroescritoriofx.appfx.producto.service.MarcaService;
import corp.jamaro.jamaroescritoriofx.appfx.producto.service.ProductoMantenimientoService;
import corp.jamaro.jamaroescritoriofx.appfx.util.AlertUtil;
import javafx.application.Platform;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.*;
import javafx.scene.layout.FlowPane;
import javafx.scene.layout.HBox;
import javafx.util.StringConverter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.controlsfx.control.textfield.AutoCompletionBinding;
import org.controlsfx.control.textfield.CustomPasswordField;
import org.controlsfx.control.textfield.CustomTextField;
import org.controlsfx.control.textfield.TextFields;
import org.springframework.stereotype.Component;

import java.net.URL;
import java.time.Duration;
import java.util.*;
import java.util.function.UnaryOperator;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
@Slf4j
public class ItemMantenimientoController extends BaseController {

    // Services
    private final ItemMantenimientoService itemMantenimientoService;
    private final MarcaService marcaService;
    private final ProductoMantenimientoService productoMantenimientoService;
    private final AlertUtil alertUtil;

    // FXML Components - Header
    @FXML private Button btnGuardar;
    @FXML private Button btnCancelar;
    @FXML private ProgressIndicator progressIndicator;
    @FXML private CustomTextField txtBuscarCodProductoOld;
    @FXML private CustomTextField txtMarca;
    @FXML private Button btnBuscarProducto1;

    // FXML Components - Main Content
    @FXML private Label lblDescripcion;
    @FXML private CustomTextField txtDescripcion;
    @FXML private FlowPane flowPaneGrupos;
    @FXML private Label lblAtributo;
    @FXML private FlowPane flowPaneAtributos;
    @FXML private CustomTextField txtBuscarCodigoFabrica;
    @FXML private FlowPane flowPaneCodigosFabrica;
    @FXML private CustomTextField txtBuscarUbicaciones;
    @FXML private FlowPane flowPaneUbicaciones;

    // FXML Components - Precios y Stock
    @FXML private CustomPasswordField txtPrecioCostoPromedio;
    @FXML private CustomPasswordField txtPrecioVentaBase;
    @FXML private CustomPasswordField txtPrecioVentaPromocion;
    @FXML private CustomPasswordField txtPrecioVentaPublico;
    @FXML private CustomPasswordField txtStockTotal;
    @FXML private CustomPasswordField txtStockDeSeguridad;
    @FXML private TextArea txtAnotaciones;

    // FXML Components - Archivos
    @FXML private Button btnAgregarImagen;
    @FXML private FlowPane flowPaneArchivos;

    // State variables
    private Producto currentProducto;
    private Item currentItem;
    private Marca currentMarca;
    private boolean isCreatingNew = false;
    private boolean isEditingMode = false;

    // AutoCompletion bindings
    private AutoCompletionBinding<Marca> marcaAutoCompletionBinding;

    // Observable lists for UI components
    private final ObservableList<Atributo> atributosData = FXCollections.observableArrayList();
    private final ObservableList<CodigoFabrica> codigosFabricaData = FXCollections.observableArrayList();
    private final ObservableList<Ubicacion> ubicacionesData = FXCollections.observableArrayList();

    // Pattern for decimal validation
    private static final Pattern DECIMAL_PATTERN = Pattern.compile("^\\d*\\.?\\d{0,2}$");

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        log.debug("Inicializando ItemMantenimientoController");
        setupInitialState();
        setupEventHandlers();
        setupValidations();
    }

    private void setupInitialState() {
        // Inicialmente todo está deshabilitado excepto el campo de búsqueda
        disableAllFields();
        txtBuscarCodProductoOld.setDisable(false);
        btnBuscarProducto1.setDisable(false);

        // Configurar estado inicial de botones
        btnGuardar.setDisable(true);
        progressIndicator.setVisible(false);

        // Limpiar datos
        clearAllData();
    }

    private void setupEventHandlers() {
        setupSearchHandlers();
        setupButtonHandlers();
        setupDescriptionHandlers();
        setupMarcaAutoCompletion();
    }

    private void setupSearchHandlers() {
        // Búsqueda por Enter en el campo principal
        txtBuscarCodProductoOld.setOnAction(event -> buscarPorCodigo());
        btnBuscarProducto1.setOnAction(event -> buscarPorCodigo());

        // Limpiar datos cuando se cambia el texto de búsqueda
        txtBuscarCodProductoOld.textProperty().addListener((obs, oldVal, newVal) -> {
            if (newVal == null || newVal.trim().isEmpty()) {
                clearAllData();
                disableAllFields();
                txtBuscarCodProductoOld.setDisable(false);
                btnBuscarProducto1.setDisable(false);
            }
        });
    }

    private void setupButtonHandlers() {
        btnGuardar.setOnAction(event -> guardarItem());
        btnCancelar.setOnAction(event -> cancelarOperacion());
        btnAgregarImagen.setOnAction(event -> agregarImagen()); // TODO: Implementar
    }

    private void setupDescriptionHandlers() {
        // Hacer editable la descripción solo con doble click
        txtDescripcion.setEditable(false);
        txtDescripcion.setOnMouseClicked(event -> {
            if (event.getClickCount() == 2 && currentItem != null) {
                txtDescripcion.setEditable(true);
                txtDescripcion.positionCaret(txtDescripcion.getText().length());
            }
        });

        // Volver a no editable cuando pierde el foco
        txtDescripcion.focusedProperty().addListener((obs, oldVal, newVal) -> {
            if (!newVal) {
                txtDescripcion.setEditable(false);
            }
        });
    }

    private void setupMarcaAutoCompletion() {
        // Configurar autocompletado para marca
        marcaAutoCompletionBinding = TextFields.bindAutoCompletion(txtMarca, request -> {
            String userText = Optional.ofNullable(request.getUserText()).orElse("").trim();
            if (userText.isEmpty()) {
                return Collections.emptyList();
            }

            runOnUiThread(() -> progressIndicator.setVisible(true));

            try {
                Optional<List<Marca>> suggestions = marcaService.buscarMarcaPorNombre(userText)
                        .collectList()
                        .onErrorReturn(Collections.emptyList())
                        .blockOptional(Duration.ofSeconds(2));

                runOnUiThread(() -> progressIndicator.setVisible(false));
                return suggestions.orElse(Collections.emptyList());
            } catch (Exception e) {
                log.error("Error buscando marcas: {}", e.getMessage(), e);
                runOnUiThread(() -> progressIndicator.setVisible(false));
                return Collections.emptyList();
            }
        });

        // Manejar selección de marca
        marcaAutoCompletionBinding.setOnAutoCompleted(event -> {
            Marca selectedMarca = event.getCompletion();
            if (selectedMarca != null && currentProducto != null) {
                currentMarca = selectedMarca;
                buscarOCrearItem();
            }
        });
    }

    private void setupValidations() {
        // Configurar validaciones numéricas para campos de precio y stock
        setupDecimalField(txtPrecioCostoPromedio);
        setupDecimalField(txtPrecioVentaBase);
        setupDecimalField(txtPrecioVentaPromocion);
        setupDecimalField(txtPrecioVentaPublico);
        setupDecimalField(txtStockTotal);
        setupDecimalField(txtStockDeSeguridad);
    }

    private void setupDecimalField(TextField field) {
        UnaryOperator<TextFormatter.Change> filter = change -> {
            String newText = change.getControlNewText();

            // Permitir texto vacío
            if (newText.isEmpty()) {
                return change;
            }

            // Validar formato decimal con máximo 2 decimales
            if (DECIMAL_PATTERN.matcher(newText).matches()) {
                return change;
            }

            return null; // Rechazar el cambio
        };

        field.setTextFormatter(new TextFormatter<>(filter));
    }

    private void buscarPorCodigo() {
        String codigo = txtBuscarCodProductoOld.getText();
        if (codigo == null || codigo.trim().isEmpty()) {
            return;
        }

        codigo = codigo.trim().toUpperCase();
        progressIndicator.setVisible(true);

        // Primero intentar buscar como Item
        subscribeMonoWithUiUpdate(
            itemMantenimientoService.buscarItemPorCodCompuesto(codigo),
            this::cargarItem,
            error -> {
                // Si no es un Item, intentar buscar como Producto
                subscribeMonoWithUiUpdate(
                    productoMantenimientoService.buscarProductoPorCodProductoOld(codigo),
                    this::cargarProducto,
                    this::handleSearchError
                );
            }
        );
    }

    private void cargarItem(Item item) {
        log.debug("Cargando Item: {}", item.getCodCompuesto());
        progressIndicator.setVisible(false);

        currentItem = item;
        currentProducto = item.getProducto();
        currentMarca = item.getMarca();
        isCreatingNew = false;
        isEditingMode = true;

        // Habilitar campos para edición
        enableItemEditingFields();

        // Cargar datos en la interfaz
        loadItemDataToUI();

        btnGuardar.setDisable(false);
    }

    private void cargarProducto(Producto producto) {
        log.debug("Cargando Producto: {}", producto.getCodProductoOld());
        progressIndicator.setVisible(false);

        currentProducto = producto;
        currentItem = null;
        currentMarca = null;
        isCreatingNew = false;
        isEditingMode = false;

        // Habilitar solo el campo de marca
        enableProductoMode();

        // Cargar datos del producto
        loadProductoDataToUI();
    }

    private void handleSearchError(Throwable error) {
        log.error("Error en búsqueda: {}", error.getMessage(), error);
        progressIndicator.setVisible(false);
        showErrorMessage("No se encontró ningún producto o item con el código especificado");
    }

    private void buscarOCrearItem() {
        if (currentProducto == null || currentMarca == null) {
            return;
        }

        String codCompuesto = currentProducto.getCodProductoOld() + currentMarca.getAbreviacion();
        progressIndicator.setVisible(true);

        subscribeMonoWithUiUpdate(
            itemMantenimientoService.buscarItemPorCodCompuesto(codCompuesto),
            this::cargarItem,
            error -> preguntarCrearNuevoItem(codCompuesto)
        );
    }

    private void preguntarCrearNuevoItem(String codCompuesto) {
        progressIndicator.setVisible(false);

        String mensaje = String.format(
            "No existe un item con el código '%s'.\n¿Desea crear un nuevo item?",
            codCompuesto
        );

        Optional<ButtonType> result = alertUtil.showConfirmation(
            "Crear Nuevo Item",
            null,
            mensaje,
            "Crear",
            "Cancelar"
        );

        if (result.isPresent() && result.get().getButtonData() == ButtonBar.ButtonData.OK_DONE) {
            inicializarModoCreacion(codCompuesto);
        }
    }

    private void inicializarModoCreacion(String codCompuesto) {
        log.debug("Inicializando modo creación para codCompuesto: {}", codCompuesto);

        // Crear nuevo Item con datos básicos
        currentItem = new Item();
        currentItem.setCodCompuesto(codCompuesto);
        currentItem.setProducto(currentProducto);
        currentItem.setMarca(currentMarca);

        // Descripción inicial
        String descripcionInicial = currentProducto.getDescripcion() + " de la marca " + currentMarca.getNombre();
        currentItem.setDescripcion(descripcionInicial);

        // Copiar atributos del producto
        if (currentProducto.getAtributos() != null) {
            Set<Atributo> atributosCopiados = currentProducto.getAtributos().stream()
                .map(this::copiarAtributo)
                .collect(Collectors.toSet());
            currentItem.setAtributos(atributosCopiados);
        }

        isCreatingNew = true;
        isEditingMode = true;

        // Habilitar campos para creación
        enableItemEditingFields();

        // Cargar datos en la interfaz
        loadItemDataToUI();

        btnGuardar.setDisable(false);
    }

    private Atributo copiarAtributo(Atributo original) {
        Atributo copia = new Atributo();
        copia.setFiltro(original.getFiltro());
        copia.setDato(original.getDato());
        return copia;
    }

    // ========== MÉTODOS DE HABILITACIÓN/DESHABILITACIÓN ==========

    private void disableAllFields() {
        txtMarca.setDisable(true);
        txtDescripcion.setDisable(true);
        txtBuscarCodigoFabrica.setDisable(true);
        txtBuscarUbicaciones.setDisable(true);

        // Campos de precios y stock
        txtPrecioCostoPromedio.setDisable(true);
        txtPrecioVentaBase.setDisable(true);
        txtPrecioVentaPromocion.setDisable(true);
        txtPrecioVentaPublico.setDisable(true);
        txtStockTotal.setDisable(true);
        txtStockDeSeguridad.setDisable(true);
        txtAnotaciones.setDisable(true);

        // Botón de imagen
        btnAgregarImagen.setDisable(true);
    }

    private void enableProductoMode() {
        // Solo habilitar el campo de marca cuando se carga un producto
        txtMarca.setDisable(false);

        // El resto permanece deshabilitado hasta seleccionar marca
        txtDescripcion.setDisable(true);
        txtBuscarCodigoFabrica.setDisable(true);
        txtBuscarUbicaciones.setDisable(true);

        txtPrecioCostoPromedio.setDisable(true);
        txtPrecioVentaBase.setDisable(true);
        txtPrecioVentaPromocion.setDisable(true);
        txtPrecioVentaPublico.setDisable(true);
        txtStockTotal.setDisable(true);
        txtStockDeSeguridad.setDisable(true);
        txtAnotaciones.setDisable(true);

        btnAgregarImagen.setDisable(true);
    }

    private void enableItemEditingFields() {
        // Habilitar todos los campos para edición de item
        txtMarca.setDisable(false);
        txtDescripcion.setDisable(false);
        txtBuscarCodigoFabrica.setDisable(false);
        txtBuscarUbicaciones.setDisable(false);

        txtPrecioCostoPromedio.setDisable(false);
        txtPrecioVentaBase.setDisable(false);
        txtPrecioVentaPromocion.setDisable(false);
        txtPrecioVentaPublico.setDisable(false);
        txtStockTotal.setDisable(false);
        txtStockDeSeguridad.setDisable(false);
        txtAnotaciones.setDisable(false);

        btnAgregarImagen.setDisable(false);
    }

    // ========== MÉTODOS DE CARGA DE DATOS EN UI ==========

    private void loadProductoDataToUI() {
        if (currentProducto == null) return;

        // Limpiar campos
        clearUIFields();

        // Cargar grupos del producto
        loadGruposToUI(currentProducto.getGrupos());

        // Limpiar marca
        txtMarca.clear();
    }

    private void loadItemDataToUI() {
        if (currentItem == null) return;

        // Cargar datos básicos
        txtDescripcion.setText(currentItem.getDescripcion());

        // Cargar marca
        if (currentItem.getMarca() != null) {
            txtMarca.setText(currentItem.getMarca().getNombre());
        }

        // Cargar grupos del producto
        if (currentItem.getProducto() != null && currentItem.getProducto().getGrupos() != null) {
            loadGruposToUI(currentItem.getProducto().getGrupos());
        }

        // Cargar atributos
        loadAtributosToUI(currentItem.getAtributos());

        // Cargar códigos de fábrica
        loadCodigosFabricaToUI(currentItem.getCodigosFabrica());

        // Cargar ubicaciones
        loadUbicacionesToUI(currentItem.getUbicaciones());

        // Cargar precios y stock
        loadPreciosYStockToUI();

        // Cargar anotaciones
        txtAnotaciones.setText(currentItem.getAnotacionesOld());

        // TODO: Cargar imágenes
    }

    private void loadGruposToUI(Set<Grupo> grupos) {
        flowPaneGrupos.getChildren().clear();

        if (grupos == null || grupos.isEmpty()) {
            return;
        }

        for (Grupo grupo : grupos) {
            Label grupoLabel = new Label(getGrupoDisplayName(grupo));
            grupoLabel.getStyleClass().addAll("tag", "tag-info");
            grupoLabel.setPadding(new Insets(4, 8, 4, 8));
            flowPaneGrupos.getChildren().add(grupoLabel);
        }
    }

    private String getGrupoDisplayName(Grupo grupo) {
        if (grupo.getNombresGrupo() != null && !grupo.getNombresGrupo().isEmpty()) {
            return grupo.getNombresGrupo().stream()
                .filter(ng -> ng.getIsPrincipal() != null && ng.getIsPrincipal())
                .findFirst()
                .map(NombreGrupo::getNombre)
                .orElse(grupo.getNombresGrupo().iterator().next().getNombre());
        }
        return grupo.getId();
    }

    private void loadAtributosToUI(Set<Atributo> atributos) {
        flowPaneAtributos.getChildren().clear();
        atributosData.clear();

        if (atributos == null || atributos.isEmpty()) {
            return;
        }

        atributosData.addAll(atributos);

        for (Atributo atributo : atributos) {
            HBox atributoContainer = createAtributoField(atributo);
            flowPaneAtributos.getChildren().add(atributoContainer);
        }
    }

    private HBox createAtributoField(Atributo atributo) {
        HBox container = new HBox(5);
        container.setAlignment(Pos.CENTER_LEFT);
        container.setPadding(new Insets(2));

        Filtro filtro = atributo.getFiltro();
        if (filtro == null) {
            return container; // Skip if no filter
        }

        TipoFiltro tipo = filtro.getTipo();
        if (tipo == null) {
            tipo = TipoFiltro.CADENA_TEXTO; // Default type
        }

        // Crear label con el nombre del filtro
        Label label = new Label(filtro.getNombreFiltro() + ":");
        label.getStyleClass().add("field-label");
        label.setMinWidth(80);

        // Crear campo según el tipo de filtro
        switch (tipo) {
            case CADENA_TEXTO:
            case OPCION_MULTIPLE:
            case NUMERICO:
                CustomTextField textField = new CustomTextField();
                textField.setText(atributo.getDato() != null ? atributo.getDato() : "");
                textField.setLeft(label);
                textField.setPrefWidth(150);

                // Configurar validación para campos numéricos
                if (tipo == TipoFiltro.NUMERICO) {
                    setupDecimalField(textField);
                }

                // Listener para actualizar el atributo
                textField.textProperty().addListener((obs, oldVal, newVal) -> {
                    atributo.setDato(newVal != null && !newVal.trim().isEmpty() ? newVal.trim() : null);
                });

                container.getChildren().add(textField);
                break;

            case DICOTOMICO:
            case COMPUESTO:
            default:
                // TODO: Implementar otros tipos de filtros
                Label todoLabel = new Label("TODO: " + tipo.name());
                todoLabel.getStyleClass().add("label-warning");
                container.getChildren().addAll(label, todoLabel);
                break;
        }

        return container;
    }

    private void loadCodigosFabricaToUI(Set<CodigoFabrica> codigosFabrica) {
        flowPaneCodigosFabrica.getChildren().clear();
        codigosFabricaData.clear();

        if (codigosFabrica == null || codigosFabrica.isEmpty()) {
            return;
        }

        codigosFabricaData.addAll(codigosFabrica);

        for (CodigoFabrica codigo : codigosFabrica) {
            Label codigoLabel = new Label(codigo.getCodigo());
            codigoLabel.getStyleClass().addAll("tag", "tag-secondary");
            codigoLabel.setPadding(new Insets(4, 8, 4, 8));
            flowPaneCodigosFabrica.getChildren().add(codigoLabel);
        }
    }

    private void loadUbicacionesToUI(Set<Ubicacion> ubicaciones) {
        flowPaneUbicaciones.getChildren().clear();
        ubicacionesData.clear();

        if (ubicaciones == null || ubicaciones.isEmpty()) {
            return;
        }

        ubicacionesData.addAll(ubicaciones);

        // Crear ToggleGroup para los radio buttons
        ToggleGroup ubicacionGroup = new ToggleGroup();

        // Ordenar ubicaciones alfabéticamente
        List<Ubicacion> ubicacionesOrdenadas = ubicaciones.stream()
            .sorted(Comparator.comparing(Ubicacion::getNombre))
            .collect(Collectors.toList());

        // Determinar cuál debe ser la principal
        Ubicacion ubicacionPrincipal = determinarUbicacionPrincipal(ubicacionesOrdenadas);

        for (Ubicacion ubicacion : ubicacionesOrdenadas) {
            HBox ubicacionContainer = new HBox(5);
            ubicacionContainer.setAlignment(Pos.CENTER_LEFT);

            RadioButton radioButton = new RadioButton();
            radioButton.setToggleGroup(ubicacionGroup);
            radioButton.setSelected(ubicacion.equals(ubicacionPrincipal));

            // Listener para actualizar la ubicación principal
            radioButton.selectedProperty().addListener((obs, oldVal, newVal) -> {
                if (newVal) {
                    actualizarUbicacionPrincipal(ubicacion);
                }
            });

            Label nombreLabel = new Label(ubicacion.getNombre());
            nombreLabel.getStyleClass().add("field-label");

            ubicacionContainer.getChildren().addAll(radioButton, nombreLabel);
            flowPaneUbicaciones.getChildren().add(ubicacionContainer);
        }
    }

    private Ubicacion determinarUbicacionPrincipal(List<Ubicacion> ubicaciones) {
        if (ubicaciones.isEmpty()) {
            return null;
        }

        // Buscar una ubicación marcada como principal
        Optional<Ubicacion> principal = ubicaciones.stream()
            .filter(u -> u.getIsPrincipal() != null && u.getIsPrincipal())
            .findFirst();

        if (principal.isPresent()) {
            return principal.get();
        }

        // Si no hay ninguna marcada como principal, tomar la primera alfabéticamente
        return ubicaciones.get(0);
    }

    private void actualizarUbicacionPrincipal(Ubicacion nuevaPrincipal) {
        // Marcar todas como no principales
        ubicacionesData.forEach(u -> u.setIsPrincipal(false));

        // Marcar la nueva como principal
        nuevaPrincipal.setIsPrincipal(true);

        log.debug("Ubicación principal actualizada a: {}", nuevaPrincipal.getNombre());
    }

    private void loadPreciosYStockToUI() {
        if (currentItem == null) return;

        txtPrecioCostoPromedio.setText(formatDecimal(currentItem.getPrecioCostoPromedio()));
        txtPrecioVentaBase.setText(formatDecimal(currentItem.getPrecioVentaBase()));
        txtPrecioVentaPromocion.setText(formatDecimal(currentItem.getPrecioVentaPromocion()));
        txtPrecioVentaPublico.setText(formatDecimal(currentItem.getPrecioVentaPublico()));
        txtStockTotal.setText(formatDecimal(currentItem.getStockTotal()));
        txtStockDeSeguridad.setText(formatDecimal(currentItem.getStockDeSeguridad()));
    }

    private String formatDecimal(Double value) {
        return value != null ? String.valueOf(value) : "";
    }

    // ========== MÉTODOS DE LIMPIEZA ==========

    private void clearAllData() {
        currentProducto = null;
        currentItem = null;
        currentMarca = null;
        isCreatingNew = false;
        isEditingMode = false;

        clearUIFields();
        clearFlowPanes();
        clearObservableLists();
    }

    private void clearUIFields() {
        txtMarca.clear();
        txtDescripcion.clear();
        txtBuscarCodigoFabrica.clear();
        txtBuscarUbicaciones.clear();

        txtPrecioCostoPromedio.clear();
        txtPrecioVentaBase.clear();
        txtPrecioVentaPromocion.clear();
        txtPrecioVentaPublico.clear();
        txtStockTotal.clear();
        txtStockDeSeguridad.clear();
        txtAnotaciones.clear();
    }

    private void clearFlowPanes() {
        flowPaneGrupos.getChildren().clear();
        flowPaneAtributos.getChildren().clear();
        flowPaneCodigosFabrica.getChildren().clear();
        flowPaneUbicaciones.getChildren().clear();
        flowPaneArchivos.getChildren().clear();
    }

    private void clearObservableLists() {
        atributosData.clear();
        codigosFabricaData.clear();
        ubicacionesData.clear();
    }

    // ========== MÉTODOS DE ACCIÓN ==========

    private void guardarItem() {
        if (currentItem == null) {
            showErrorMessage("No hay item para guardar");
            return;
        }

        // Validar datos antes de guardar
        if (!validarDatos()) {
            return;
        }

        // Actualizar item con datos de la UI
        actualizarItemConDatosUI();

        progressIndicator.setVisible(true);
        btnGuardar.setDisable(true);

        if (isCreatingNew) {
            // Crear nuevo item
            subscribeMonoWithUiUpdate(
                itemMantenimientoService.crearItem(currentItem),
                this::onItemGuardado,
                this::onErrorGuardando
            );
        } else {
            // Actualizar item existente
            subscribeMonoWithUiUpdate(
                itemMantenimientoService.actualizarItem(currentItem),
                this::onItemGuardado,
                this::onErrorGuardando
            );
        }
    }

    private boolean validarDatos() {
        if (currentItem.getDescripcion() == null || currentItem.getDescripcion().trim().isEmpty()) {
            showErrorMessage("La descripción es obligatoria");
            return false;
        }

        if (currentItem.getMarca() == null) {
            showErrorMessage("La marca es obligatoria");
            return false;
        }

        if (currentItem.getProducto() == null) {
            showErrorMessage("El producto es obligatorio");
            return false;
        }

        return true;
    }

    private void actualizarItemConDatosUI() {
        // Actualizar descripción
        currentItem.setDescripcion(txtDescripcion.getText());

        // Actualizar precios y stock
        currentItem.setPrecioCostoPromedio(parseDecimal(txtPrecioCostoPromedio.getText()));
        currentItem.setPrecioVentaBase(parseDecimal(txtPrecioVentaBase.getText()));
        currentItem.setPrecioVentaPromocion(parseDecimal(txtPrecioVentaPromocion.getText()));
        currentItem.setPrecioVentaPublico(parseDecimal(txtPrecioVentaPublico.getText()));
        currentItem.setStockTotal(parseDecimal(txtStockTotal.getText()));
        currentItem.setStockDeSeguridad(parseDecimal(txtStockDeSeguridad.getText()));

        // Actualizar anotaciones
        currentItem.setAnotacionesOld(txtAnotaciones.getText());

        // Los atributos ya se actualizan automáticamente a través de los listeners
        // Las ubicaciones también se actualizan automáticamente
    }

    private Double parseDecimal(String text) {
        if (text == null || text.trim().isEmpty()) {
            return null;
        }
        try {
            return Double.parseDouble(text.trim());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    private void onItemGuardado(Item itemGuardado) {
        progressIndicator.setVisible(false);
        btnGuardar.setDisable(false);

        log.info("Item guardado exitosamente: {}", itemGuardado.getCodCompuesto());

        String mensaje = isCreatingNew ?
            "Item creado exitosamente" :
            "Item actualizado exitosamente";

        AlertUtil.showInformation("Éxito", mensaje);

        // Actualizar estado
        currentItem = itemGuardado;
        isCreatingNew = false;
    }

    private void onErrorGuardando(Throwable error) {
        progressIndicator.setVisible(false);
        btnGuardar.setDisable(false);

        log.error("Error guardando item: {}", error.getMessage(), error);
        showErrorMessage("Error guardando el item: " + error.getMessage());
    }

    private void cancelarOperacion() {
        Optional<ButtonType> result = AlertUtil.showConfirmation(
            "Cancelar",
            "¿Está seguro que desea cancelar? Se perderán los cambios no guardados."
        );

        if (result.isPresent() && result.get() == ButtonType.OK) {
            clearAllData();
            setupInitialState();
        }
    }

    private void agregarImagen() {
        // TODO: Implementar funcionalidad de agregar imágenes
        AlertUtil.showInformation("Pendiente", "Funcionalidad de imágenes pendiente de implementación");
    }

    // ========== MÉTODOS AUXILIARES ==========

    private void showErrorMessage(String message) {
        AlertUtil.showError("Error", message);
    }

    // ========== MÉTODOS PÚBLICOS PARA INTEGRACIÓN ==========

    /**
     * Método para cargar un Producto externamente
     */
    public void setProducto(Producto producto) {
        if (producto != null) {
            txtBuscarCodProductoOld.setText(producto.getCodProductoOld());
            cargarProducto(producto);
        }
    }

    /**
     * Método para cargar un Item externamente
     */
    public void setItem(Item item) {
        if (item != null) {
            txtBuscarCodProductoOld.setText(item.getCodCompuesto());
            cargarItem(item);
        }
    }
}
